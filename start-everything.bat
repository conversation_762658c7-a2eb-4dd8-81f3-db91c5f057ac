@echo off
echo ========================================
echo Starting Rise of Kingdoms Bot System
echo ========================================
echo.

REM Check if required files exist
if not exist "index.exe" (
    echo ERROR: index.exe not found!
    pause
    exit /b 1
)

if not exist "game-keeper.ps1" (
    echo ERROR: game-keeper.ps1 not found!
    pause
    exit /b 1
)

if not exist "platform-tools\adb.exe" (
    echo ERROR: platform-tools\adb.exe not found!
    pause
    exit /b 1
)

echo ✅ All required files found
echo.

echo Starting Game Keeper (background process)...
start /min "Game Keeper" powershell.exe -ExecutionPolicy Bypass -WindowStyle Minimized -File "game-keeper.ps1" -CheckInterval 30

echo Waiting 3 seconds for <PERSON> Keeper to initialize...
timeout /t 3 /nobreak >nul

echo.
echo Starting Main Bot...
echo ========================================
echo.

REM Start your main bot
start.bat

echo.
echo ========================================
echo Both processes started successfully!
echo - Main Bot: Running in this window
echo - Game Keeper: Running minimized in background
echo.
echo To stop Game Keeper, close its PowerShell window
echo or use Task Manager to end the PowerShell process
echo ========================================
