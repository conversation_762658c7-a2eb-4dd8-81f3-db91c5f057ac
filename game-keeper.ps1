# ============================================================================
# Rise of Kingdoms Game Keeper Script
# ============================================================================
# This script monitors the Rise of Kingdoms game and automatically restarts
# it when it closes. Designed to run 24/7 on your VPS.
# ============================================================================

param(
    [int]$CheckInterval = 30,           # Check interval in seconds
    [int]$RestartDelay = 30,            # Delay after restart in seconds
    [int]$MaxRestartAttempts = 5,       # Max consecutive restart attempts
    [int]$CooldownMinutes = 5,          # Cooldown period after max attempts
    [string]$LogFile = "game-keeper.log", # Log file path
    [switch]$Verbose = $false           # Enable verbose logging
)

# Configuration
$GAME_PACKAGE = "com.lilithgame.roc.gp"
$ADB_PATH = ".\platform-tools\adb.exe"
$BLUESTACKS_PATH = "C:\Program Files\BlueStacks_nxt\HD-Player.exe"

# Global variables
$script:RestartAttempts = 0
$script:LastRestartTime = [DateTime]::MinValue
$script:TotalRestarts = 0
$script:StartTime = Get-Date

# ============================================================================
# Logging Functions
# ============================================================================

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logEntry = "[$timestamp] [$Level] $Message"
    
    # Write to console with colors
    switch ($Level) {
        "ERROR" { Write-Host $logEntry -ForegroundColor Red }
        "WARN"  { Write-Host $logEntry -ForegroundColor Yellow }
        "SUCCESS" { Write-Host $logEntry -ForegroundColor Green }
        default { Write-Host $logEntry -ForegroundColor White }
    }
    
    # Write to log file
    try {
        Add-Content -Path $LogFile -Value $logEntry -ErrorAction SilentlyContinue
    } catch {
        # Ignore log file errors to prevent script failure
    }
}

function Write-VerboseLog {
    param([string]$Message)
    if ($Verbose) {
        Write-Log $Message "DEBUG"
    }
}

# ============================================================================
# Game Monitoring Functions
# ============================================================================

function Test-AdbConnection {
    try {
        $devices = & $ADB_PATH devices 2>$null
        if ($LASTEXITCODE -eq 0 -and $devices -match "device$") {
            Write-VerboseLog "ADB connection verified"
            return $true
        } else {
            Write-Log "ADB connection failed or no devices found" "WARN"
            return $false
        }
    } catch {
        Write-Log "Error testing ADB connection: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Get-FocusedApp {
    try {
        $command = "dumpsys activity activities | grep mResumedActivity"
        $output = & $ADB_PATH shell $command 2>$null
        
        if ($LASTEXITCODE -eq 0 -and $output) {
            # Extract package name from output like: mResumedActivity: ActivityRecord{abc123 u0 com.lilithgame.roc.gp/...}
            if ($output -match "mResumedActivity.*?(\S+)/") {
                $packageName = $matches[1]
                Write-VerboseLog "Focused app: $packageName"
                return $packageName
            }
        }
        
        Write-VerboseLog "Could not determine focused app"
        return $null
    } catch {
        Write-Log "Error getting focused app: $($_.Exception.Message)" "ERROR"
        return $null
    }
}

function Test-GameRunning {
    $focusedApp = Get-FocusedApp
    $isRunning = $focusedApp -eq $GAME_PACKAGE
    
    Write-VerboseLog "Game running check: $(if($isRunning){'YES'}else{'NO'})"
    return $isRunning
}

# ============================================================================
# Game Restart Functions
# ============================================================================

function Start-GameWithMonkey {
    try {
        Write-Log "Starting game using monkey command..."
        $output = & $ADB_PATH shell "monkey -p $GAME_PACKAGE 1" 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Log "Monkey command executed successfully" "SUCCESS"
            return $true
        } else {
            Write-Log "Monkey command failed: $output" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Error executing monkey command: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Start-GameWithBlueStacks {
    try {
        if (-not (Test-Path $BLUESTACKS_PATH)) {
            Write-Log "BlueStacks executable not found at: $BLUESTACKS_PATH" "WARN"
            return $false
        }
        
        Write-Log "Starting game using BlueStacks launcher..."
        $process = Start-Process -FilePath $BLUESTACKS_PATH -ArgumentList "--cmd", "launchApp", "--package", $GAME_PACKAGE -PassThru -WindowStyle Hidden
        
        # Wait a moment for the command to execute
        Start-Sleep -Seconds 5
        
        if (-not $process.HasExited) {
            Write-Log "BlueStacks launcher command executed" "SUCCESS"
            return $true
        } else {
            Write-Log "BlueStacks launcher failed" "ERROR"
            return $false
        }
    } catch {
        Write-Log "Error using BlueStacks launcher: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Restart-Game {
    $currentTime = Get-Date
    
    # Check cooldown period
    if ($script:RestartAttempts -ge $MaxRestartAttempts) {
        $timeSinceLastRestart = ($currentTime - $script:LastRestartTime).TotalMinutes
        if ($timeSinceLastRestart -lt $CooldownMinutes) {
            $remainingCooldown = [math]::Ceiling($CooldownMinutes - $timeSinceLastRestart)
            Write-Log "In cooldown period. $remainingCooldown minutes remaining." "WARN"
            return $false
        } else {
            Write-Log "Cooldown period ended. Resetting restart attempts." "INFO"
            $script:RestartAttempts = 0
        }
    }
    
    $script:RestartAttempts++
    $script:LastRestartTime = $currentTime
    $script:TotalRestarts++
    
    Write-Log "🔄 Attempting to restart game (Attempt $script:RestartAttempts/$MaxRestartAttempts)" "WARN"
    
    # Try monkey command first
    $success = Start-GameWithMonkey
    
    # If monkey fails, try BlueStacks launcher
    if (-not $success) {
        Write-Log "Monkey command failed, trying BlueStacks launcher..." "WARN"
        $success = Start-GameWithBlueStacks
    }
    
    if ($success) {
        Write-Log "⏳ Waiting $RestartDelay seconds for game to start..." "INFO"
        Start-Sleep -Seconds $RestartDelay
        
        # Verify game is now running
        if (Test-GameRunning) {
            Write-Log "✅ Game successfully restarted and verified running!" "SUCCESS"
            $script:RestartAttempts = 0  # Reset on successful restart
            return $true
        } else {
            Write-Log "⚠️ Game restart command succeeded but game not detected as running" "WARN"
            return $false
        }
    } else {
        Write-Log "❌ All restart methods failed" "ERROR"
        
        if ($script:RestartAttempts -ge $MaxRestartAttempts) {
            Write-Log "🛑 Maximum restart attempts reached. Entering cooldown period for $CooldownMinutes minutes." "ERROR"
        }
        
        return $false
    }
}

# ============================================================================
# Statistics and Status Functions
# ============================================================================

function Show-Statistics {
    $uptime = (Get-Date) - $script:StartTime
    $uptimeStr = "{0:dd} days, {0:hh} hours, {0:mm} minutes" -f $uptime
    
    Write-Log "📊 === GAME KEEPER STATISTICS ===" "INFO"
    Write-Log "📊 Uptime: $uptimeStr" "INFO"
    Write-Log "📊 Total Restarts: $script:TotalRestarts" "INFO"
    Write-Log "📊 Current Restart Attempts: $script:RestartAttempts/$MaxRestartAttempts" "INFO"
    Write-Log "📊 Last Restart: $(if($script:LastRestartTime -eq [DateTime]::MinValue){'Never'}else{$script:LastRestartTime.ToString('yyyy-MM-dd HH:mm:ss')})" "INFO"
    Write-Log "📊 =================================" "INFO"
}

# ============================================================================
# Main Monitoring Loop
# ============================================================================

function Start-GameKeeper {
    Write-Log "🎮 Rise of Kingdoms Game Keeper Started" "SUCCESS"
    Write-Log "📋 Configuration:" "INFO"
    Write-Log "   - Check Interval: $CheckInterval seconds" "INFO"
    Write-Log "   - Restart Delay: $RestartDelay seconds" "INFO"
    Write-Log "   - Max Restart Attempts: $MaxRestartAttempts" "INFO"
    Write-Log "   - Cooldown Period: $CooldownMinutes minutes" "INFO"
    Write-Log "   - Log File: $LogFile" "INFO"
    Write-Log "   - Verbose Logging: $Verbose" "INFO"
    Write-Log "🚀 Starting monitoring loop..." "SUCCESS"
    
    $statisticsCounter = 0
    $statisticsInterval = 20  # Show statistics every 20 checks
    
    while ($true) {
        try {
            # Show statistics periodically
            if ($statisticsCounter % $statisticsInterval -eq 0) {
                Show-Statistics
            }
            $statisticsCounter++
            
            # Check ADB connection first
            if (-not (Test-AdbConnection)) {
                Write-Log "ADB connection lost. Retrying in $CheckInterval seconds..." "WARN"
                Start-Sleep -Seconds $CheckInterval
                continue
            }
            
            # Check if game is running
            $isGameRunning = Test-GameRunning
            
            if (-not $isGameRunning) {
                Write-Log "🔴 Game is NOT running!" "WARN"
                
                $restartSuccess = Restart-Game
                
                if (-not $restartSuccess) {
                    Write-Log "⏭️ Restart failed. Will retry on next check cycle." "WARN"
                }
            } else {
                Write-VerboseLog "✅ Game is running normally"
                
                # Reset restart attempts when game is running normally
                if ($script:RestartAttempts -gt 0) {
                    Write-Log "✅ Game running normally. Resetting restart attempts." "SUCCESS"
                    $script:RestartAttempts = 0
                }
            }
            
            # Wait before next check
            Start-Sleep -Seconds $CheckInterval
            
        } catch {
            Write-Log "💥 Unexpected error in monitoring loop: $($_.Exception.Message)" "ERROR"
            Write-Log "⏳ Waiting $CheckInterval seconds before retrying..." "WARN"
            Start-Sleep -Seconds $CheckInterval
        }
    }
}

# ============================================================================
# Script Entry Point
# ============================================================================

# Validate ADB path
if (-not (Test-Path $ADB_PATH)) {
    Write-Log "❌ ADB executable not found at: $ADB_PATH" "ERROR"
    Write-Log "Please ensure the platform-tools directory exists in the current directory." "ERROR"
    exit 1
}

# Handle Ctrl+C gracefully
$null = Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Write-Log "🛑 Game Keeper shutting down..." "WARN"
    Show-Statistics
}

# Start the game keeper
try {
    Start-GameKeeper
} catch {
    Write-Log "💥 Fatal error: $($_.Exception.Message)" "ERROR"
    Show-Statistics
    exit 1
}
