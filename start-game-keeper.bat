@echo off
echo ========================================
echo Rise of Kingdoms Game Keeper
echo ========================================
echo.
echo Starting Game Keeper with default settings...
echo - Check interval: 30 seconds
echo - Restart delay: 30 seconds  
echo - Max restart attempts: 5
echo - Cooldown period: 5 minutes
echo.
echo Press Ctrl+C to stop the script
echo ========================================
echo.

REM Run the PowerShell script with execution policy bypass
powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1"

echo.
echo Game Keeper has stopped.
pause
