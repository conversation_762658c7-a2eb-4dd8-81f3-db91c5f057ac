import { setTimeout } from "node:timers/promises";
import type { <PERSON><PERSON> } from "pino";
import type { Devi<PERSON> } from "adb-ts";
import { execAsync } from "./util/exec-async.js";
import { config } from "./config.js";

const RISE_OF_KINGDOMS_PACKAGE_NAME = "com.lilithgame.roc.gp";
const DEFAULT_MONITOR_INTERVAL = 30_000; // 30 seconds
const GAME_BOOT_TIMEOUT = 30_000; // 30 seconds
const MAX_RESTART_ATTEMPTS = 3;
const RESTART_COOLDOWN = 60_000; // 1 minute between restart attempts

export interface GameMonitorOptions {
  device: Device;
  logger: Logger;
  monitorInterval?: number;
  enabled?: boolean;
}

export class GameMonitor {
  private device: Device;
  private logger: Logger;
  private monitorInterval: number;
  private enabled: boolean;
  private isMonitoring: boolean = false;
  private restartAttempts: number = 0;
  private lastRestartTime: number = 0;

  constructor(options: GameMonitorOptions) {
    this.device = options.device;
    this.logger = options.logger;
    this.monitorInterval = options.monitorInterval || DEFAULT_MONITOR_INTERVAL;
    this.enabled = options.enabled ?? true;
  }

  /**
   * Start monitoring the game state
   */
  public async start(): Promise<void> {
    if (!this.enabled) {
      this.logger.info("Game monitoring is disabled");
      return;
    }

    if (this.isMonitoring) {
      this.logger.warn("Game monitoring is already running");
      return;
    }

    this.isMonitoring = true;
    this.logger.info(`Starting game monitoring with ${this.monitorInterval}ms interval`);
    
    // Start the monitoring loop
    this.monitorLoop().catch((error) => {
      this.logger.error("Game monitoring loop failed:", error);
      this.isMonitoring = false;
    });
  }

  /**
   * Stop monitoring the game state
   */
  public stop(): void {
    this.isMonitoring = false;
    this.logger.info("Game monitoring stopped");
  }

  /**
   * Check if the game is currently running
   */
  private async isGameRunning(): Promise<boolean> {
    try {
      const focussedAppPackageName = await this.device.shell(
        `dumpsys activity activities | grep mResumedActivity | cut -d "{" -f2 | cut -d ' ' -f3 | cut -d "/" -f1`
      );

      const isRunning = focussedAppPackageName.includes(RISE_OF_KINGDOMS_PACKAGE_NAME);
      this.logger.debug(`Game running check: ${isRunning ? 'YES' : 'NO'} (focused app: ${focussedAppPackageName.trim()})`);

      return isRunning;
    } catch (error) {
      this.logger.error("Failed to check game state:", error);
      return false;
    }
  }

  /**
   * Restart the game
   */
  private async restartGame(): Promise<boolean> {
    const now = Date.now();
    
    // Check if we're in cooldown period
    if (now - this.lastRestartTime < RESTART_COOLDOWN) {
      this.logger.warn("Game restart is in cooldown period");
      return false;
    }

    // Check if we've exceeded max restart attempts
    if (this.restartAttempts >= MAX_RESTART_ATTEMPTS) {
      this.logger.error(`Maximum restart attempts (${MAX_RESTART_ATTEMPTS}) reached. Stopping automatic restarts.`);
      return false;
    }

    try {
      this.logger.info(`Attempting to restart game (attempt ${this.restartAttempts + 1}/${MAX_RESTART_ATTEMPTS})`);
      this.lastRestartTime = now;
      this.restartAttempts++;

      // Launch the game using monkey command
      await this.device.execShell(`monkey -p ${RISE_OF_KINGDOMS_PACKAGE_NAME} 1`);
      
      // Wait for the game to boot
      await setTimeout(GAME_BOOT_TIMEOUT);
      
      // Verify the game is now running
      const isRunning = await this.isGameRunning();
      
      if (isRunning) {
        this.logger.info("Game successfully restarted");
        this.restartAttempts = 0; // Reset attempts on successful restart
        return true;
      } else {
        this.logger.warn("Game restart may have failed - game not detected as running");
        return false;
      }
    } catch (error) {
      this.logger.error("Failed to restart game:", error);
      return false;
    }
  }

  /**
   * Alternative restart method using BlueStacks launcher
   */
  private async restartGameWithBlueStacks(): Promise<boolean> {
    try {
      this.logger.info("Attempting to restart game using BlueStacks launcher");
      
      // This is a more robust restart method that launches the app through BlueStacks
      const command = `"${config.BLUESTACKS_EXECUTABLE}\\HD-Player.exe" --cmd launchApp --package "${RISE_OF_KINGDOMS_PACKAGE_NAME}"`;
      
      await execAsync(command, { shell: "cmd", timeout: 30000 });
      
      // Wait for the game to boot
      await setTimeout(GAME_BOOT_TIMEOUT);
      
      // Verify the game is now running
      const isRunning = await this.isGameRunning();
      
      if (isRunning) {
        this.logger.info("Game successfully restarted using BlueStacks launcher");
        this.restartAttempts = 0; // Reset attempts on successful restart
        return true;
      } else {
        this.logger.warn("BlueStacks restart may have failed - game not detected as running");
        return false;
      }
    } catch (error) {
      this.logger.error("Failed to restart game with BlueStacks:", error);
      return false;
    }
  }

  /**
   * Main monitoring loop
   */
  private async monitorLoop(): Promise<void> {
    this.logger.info("Game monitoring loop started");

    while (this.isMonitoring) {
      try {
        const isRunning = await this.isGameRunning();

        if (!isRunning) {
          this.logger.warn("🔴 Game is not running, attempting to restart...");

          // Try the simple restart first
          let restartSuccess = await this.restartGame();

          // If simple restart fails, try BlueStacks launcher
          if (!restartSuccess && config.BLUESTACKS_EXECUTABLE) {
            this.logger.info("Trying alternative restart method with BlueStacks launcher...");
            restartSuccess = await this.restartGameWithBlueStacks();
          }

          if (!restartSuccess) {
            this.logger.error("❌ All restart attempts failed. Will retry on next monitoring cycle.");

            // If we've hit max attempts, wait longer before next check
            if (this.restartAttempts >= MAX_RESTART_ATTEMPTS) {
              this.logger.warn(`⏳ Maximum restart attempts reached. Waiting ${RESTART_COOLDOWN / 1000}s before next attempt.`);
              await setTimeout(RESTART_COOLDOWN);
            }
          } else {
            this.logger.info("✅ Game successfully restarted and is now running");
          }
        } else {
          // Game is running, reset restart attempts
          if (this.restartAttempts > 0) {
            this.logger.info("✅ Game is running normally, resetting restart attempts");
            this.restartAttempts = 0;
          }
        }

        // Wait before next check
        await setTimeout(this.monitorInterval);
      } catch (error) {
        this.logger.error("💥 Error in monitoring loop:", error);

        // On critical errors, wait a bit longer before retrying
        await setTimeout(Math.min(this.monitorInterval * 2, 60000));
      }
    }

    this.logger.info("Game monitoring loop stopped");
  }

  /**
   * Get current monitoring status
   */
  public getStatus(): {
    isMonitoring: boolean;
    enabled: boolean;
    restartAttempts: number;
    lastRestartTime: number;
  } {
    return {
      isMonitoring: this.isMonitoring,
      enabled: this.enabled,
      restartAttempts: this.restartAttempts,
      lastRestartTime: this.lastRestartTime,
    };
  }

  /**
   * Enable or disable monitoring
   */
  public setEnabled(enabled: boolean): void {
    this.enabled = enabled;
    if (!enabled && this.isMonitoring) {
      this.stop();
    }
    this.logger.info(`Game monitoring ${enabled ? 'enabled' : 'disabled'}`);
  }

  /**
   * Update monitoring interval
   */
  public setMonitorInterval(interval: number): void {
    this.monitorInterval = interval;
    this.logger.info(`Game monitoring interval updated to ${interval}ms`);
  }
}
