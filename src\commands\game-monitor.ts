import {
  Slash<PERSON>ommandBuilder,
  ChatInputCommandInteraction,
  PermissionFlagsBits,
} from "discord.js";
import type { AppContext } from "../types.js";
import { config } from "../config.js";

const OPTION_ACTION_NAME = "action";
const OPTION_INTERVAL_NAME = "interval";

export const gameMonitorCommand = {
  name: "game-monitor",
  data: new SlashCommandBuilder()
    .setName("game-monitor")
    .setDescription("Control the game monitoring system")
    .setDefaultMemberPermissions(PermissionFlagsBits.Administrator)
    .addStringOption((option) =>
      option
        .setName(OPTION_ACTION_NAME)
        .setDescription("Action to perform")
        .setRequired(true)
        .addChoices(
          { name: "Status", value: "status" },
          { name: "Enable", value: "enable" },
          { name: "Disable", value: "disable" },
          { name: "Restart", value: "restart" }
        )
    )
    .addIntegerOption((option) =>
      option
        .setName(OPTION_INTERVAL_NAME)
        .setDescription("Monitoring interval in seconds (only for enable action)")
        .setRequired(false)
        .setMinValue(10)
        .setMaxValue(300)
    ),

  async execute(
    interaction: ChatInputCommandInteraction,
    context: AppContext
  ) {
    await interaction.deferReply();

    const action = interaction.options.getString(OPTION_ACTION_NAME, true);
    const interval = interaction.options.getInteger(OPTION_INTERVAL_NAME);

    const gameMonitor = context.gameMonitor;

    if (!gameMonitor) {
      await interaction.followUp({
        content: "❌ Game monitor is not available. Please check the application logs.",
        ephemeral: true,
      });
      return;
    }

    try {
      switch (action) {
        case "status": {
          const status = gameMonitor.getStatus();
          const embed = {
            title: "🎮 Game Monitor Status",
            color: status.isMonitoring ? 0x00ff00 : 0xff0000,
            fields: [
              {
                name: "Status",
                value: status.isMonitoring ? "🟢 Running" : "🔴 Stopped",
                inline: true,
              },
              {
                name: "Enabled",
                value: status.enabled ? "✅ Yes" : "❌ No",
                inline: true,
              },
              {
                name: "Restart Attempts",
                value: status.restartAttempts.toString(),
                inline: true,
              },
              {
                name: "Last Restart",
                value: status.lastRestartTime
                  ? new Date(status.lastRestartTime).toLocaleString()
                  : "Never",
                inline: false,
              },
            ],
            timestamp: new Date().toISOString(),
          };

          await interaction.followUp({ embeds: [embed] });
          break;
        }

        case "enable": {
          const monitorInterval = interval ? interval * 1000 : config.GAME_MONITOR_INTERVAL;
          
          gameMonitor.setEnabled(true);
          if (interval) {
            gameMonitor.setMonitorInterval(monitorInterval);
          }
          
          await gameMonitor.start();

          await interaction.followUp({
            content: `✅ Game monitoring enabled with ${monitorInterval / 1000}s interval`,
          });
          break;
        }

        case "disable": {
          gameMonitor.setEnabled(false);

          await interaction.followUp({
            content: "❌ Game monitoring disabled",
          });
          break;
        }

        case "restart": {
          gameMonitor.stop();
          await gameMonitor.start();

          await interaction.followUp({
            content: "🔄 Game monitoring restarted",
          });
          break;
        }

        default:
          await interaction.followUp({
            content: "❌ Invalid action specified",
            ephemeral: true,
          });
      }
    } catch (error) {
      context.logger.error("Game monitor command error:", error);
      await interaction.followUp({
        content: "❌ An error occurred while executing the command. Please check the logs.",
        ephemeral: true,
      });
    }
  },
};
