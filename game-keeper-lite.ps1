# ============================================================================
# Rise of Kingdoms Game Keeper - LITE VERSION
# Ultra-lightweight version optimized for minimal VPS resource usage
# ============================================================================

param(
    [int]$CheckInterval = 30,
    [switch]$Silent = $false
)

# Configuration
$GAME_PACKAGE = "com.lilithgame.roc.gp"
$ADB_PATH = ".\platform-tools\adb.exe"

# Counters
$script:RestartCount = 0
$script:CheckCount = 0

function Write-Status {
    param([string]$Message, [string]$Type = "INFO")
    if (-not $Silent) {
        $timestamp = Get-Date -Format "HH:mm:ss"
        Write-Host "[$timestamp] $Message" -ForegroundColor $(if($Type -eq "ERROR"){"Red"}elseif($Type -eq "SUCCESS"){"Green"}else{"White"})
    }
}

function Test-GameRunning {
    try {
        $output = & $ADB_PATH shell "dumpsys activity activities | grep mResumedActivity" 2>$null
        return $output -match $GAME_PACKAGE
    } catch {
        return $false
    }
}

function Restart-Game {
    try {
        Write-Status "🔄 Restarting game..." "ERROR"
        & $ADB_PATH shell "monkey -p $GAME_PACKAGE 1" 2>$null | Out-Null
        Start-Sleep -Seconds 25
        
        if (Test-GameRunning) {
            $script:RestartCount++
            Write-Status "✅ Game restarted successfully (Total: $script:RestartCount)" "SUCCESS"
            return $true
        }
        return $false
    } catch {
        return $false
    }
}

# Main loop
Write-Status "🎮 Game Keeper LITE started (Check every ${CheckInterval}s)"

while ($true) {
    try {
        $script:CheckCount++
        
        if (-not (Test-GameRunning)) {
            Restart-Game
        } elseif ($script:CheckCount % 20 -eq 0) {
            # Status update every 20 checks (10 minutes with 30s interval)
            Write-Status "✅ Game running OK (Checks: $script:CheckCount, Restarts: $script:RestartCount)"
        }
        
        Start-Sleep -Seconds $CheckInterval
    } catch {
        Start-Sleep -Seconds $CheckInterval
    }
}
