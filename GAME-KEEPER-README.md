# Rise of Kingdoms Game Keeper

An advanced PowerShell script that monitors Rise of Kingdoms and automatically restarts it when the game closes. Perfect for running 24/7 on your VPS to ensure the game stays open for your bot operations.

## Features

- 🎮 **Automatic Game Monitoring** - Continuously checks if Rise of Kingdoms is running
- 🔄 **Smart Restart Logic** - Multiple restart methods with fallback options
- 📊 **Detailed Logging** - Comprehensive logs with timestamps and statistics
- ⚡ **Configurable Settings** - Customizable check intervals, restart attempts, and cooldowns
- 🛡️ **Error Handling** - Robust error handling and recovery mechanisms
- 📈 **Statistics Tracking** - Uptime, restart counts, and performance metrics
- 🔧 **Multiple Start Options** - Easy-to-use batch files for different scenarios

## Quick Start

### Method 1: Simple Start (Recommended)
1. Copy all files to your VPS in the same directory as your main bot
2. Double-click `start-game-keeper.bat`
3. The script will start monitoring with default settings

### Method 2: Advanced Configuration
1. Double-click `start-game-keeper-advanced.bat`
2. Choose from various configuration options
3. Customize settings as needed

### Method 3: Command Line
```powershell
# Basic usage
powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1"

# With custom settings
powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1" -CheckInterval 20 -Verbose
```

## Configuration Options

| Parameter | Default | Description |
|-----------|---------|-------------|
| `CheckInterval` | 30 | How often to check if game is running (seconds) |
| `RestartDelay` | 30 | Wait time after restarting game (seconds) |
| `MaxRestartAttempts` | 5 | Maximum consecutive restart attempts |
| `CooldownMinutes` | 5 | Cooldown period after max attempts (minutes) |
| `LogFile` | "game-keeper.log" | Path to log file |
| `Verbose` | false | Enable detailed debug logging |

## Examples

### Quick Monitoring (10-second checks)
```powershell
powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1" -CheckInterval 10 -RestartDelay 20
```

### Verbose Logging
```powershell
powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1" -Verbose
```

### Conservative Settings (slower but more stable)
```powershell
powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1" -CheckInterval 60 -RestartDelay 45 -MaxRestartAttempts 3
```

## How It Works

1. **Game Detection**: Uses ADB to check which app is currently focused
2. **Restart Methods**: 
   - Primary: ADB monkey command (`monkey -p com.lilithgame.roc.gp 1`)
   - Fallback: BlueStacks launcher command
3. **Smart Cooldowns**: Prevents excessive restart attempts
4. **Verification**: Confirms game is actually running after restart

## Log Output Examples

```
[2024-01-15 14:30:15] [INFO] 🎮 Rise of Kingdoms Game Keeper Started
[2024-01-15 14:30:45] [WARN] 🔴 Game is NOT running!
[2024-01-15 14:30:45] [WARN] 🔄 Attempting to restart game (Attempt 1/5)
[2024-01-15 14:30:45] [SUCCESS] Monkey command executed successfully
[2024-01-15 14:31:15] [SUCCESS] ✅ Game successfully restarted and verified running!
```

## Requirements

- Windows VPS with PowerShell
- BlueStacks emulator installed and configured
- ADB platform-tools in `.\platform-tools\` directory
- Rise of Kingdoms installed in BlueStacks

## File Structure

```
your-bot-directory/
├── game-keeper.ps1                 # Main PowerShell script
├── start-game-keeper.bat          # Simple start script
├── start-game-keeper-advanced.bat # Advanced start script with options
├── GAME-KEEPER-README.md          # This documentation
├── platform-tools/                # ADB tools directory
│   └── adb.exe
└── game-keeper.log                # Generated log file
```

## Troubleshooting

### Game Keeper Won't Start
- Ensure PowerShell execution policy allows scripts
- Check that `platform-tools/adb.exe` exists
- Verify BlueStacks is installed at the default location

### Game Not Restarting
- Check ADB connection: `.\platform-tools\adb.exe devices`
- Verify game package name is correct
- Check BlueStacks executable path in script

### High CPU Usage
- Increase `CheckInterval` to reduce frequency
- Disable verbose logging if enabled

## Advanced Usage

### Running as Windows Service
You can use tools like NSSM to run the script as a Windows service for automatic startup.

### Custom BlueStacks Path
Edit the script and modify the `$BLUESTACKS_PATH` variable if your installation is in a different location.

### Integration with Task Scheduler
Set up Windows Task Scheduler to automatically start the script on system boot.

## Support

If you encounter issues:
1. Check the `game-keeper.log` file for detailed error messages
2. Run with `-Verbose` flag for additional debugging information
3. Ensure all prerequisites are properly installed

## License

This script is provided as-is for educational and personal use.
