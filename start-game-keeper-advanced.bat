@echo off
setlocal

echo ========================================
echo Rise of Kingdoms Game Keeper - Advanced
echo ========================================
echo.

REM Default values
set CHECK_INTERVAL=30
set RESTART_DELAY=30
set MAX_ATTEMPTS=5
set COOLDOWN_MINUTES=5
set VERBOSE=false

echo Current settings:
echo - Check interval: %CHECK_INTERVAL% seconds
echo - Restart delay: %RESTART_DELAY% seconds
echo - Max restart attempts: %MAX_ATTEMPTS%
echo - Cooldown period: %COOLDOWN_MINUTES% minutes
echo - Verbose logging: %VERBOSE%
echo.

echo Options:
echo 1. Start with default settings
echo 2. Start with custom settings
echo 3. Start with verbose logging
echo 4. Quick start (10 second intervals)
echo 5. Exit
echo.

set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto start_default
if "%choice%"=="2" goto custom_settings
if "%choice%"=="3" goto start_verbose
if "%choice%"=="4" goto quick_start
if "%choice%"=="5" goto exit
goto invalid_choice

:custom_settings
echo.
echo === Custom Settings ===
set /p CHECK_INTERVAL="Check interval in seconds (current: %CHECK_INTERVAL%): "
set /p RESTART_DELAY="Restart delay in seconds (current: %RESTART_DELAY%): "
set /p MAX_ATTEMPTS="Max restart attempts (current: %MAX_ATTEMPTS%): "
set /p COOLDOWN_MINUTES="Cooldown period in minutes (current: %COOLDOWN_MINUTES%): "
echo.
goto start_custom

:start_verbose
set VERBOSE=true
echo Starting with verbose logging enabled...
goto start_custom

:quick_start
set CHECK_INTERVAL=10
set RESTART_DELAY=20
echo Starting with quick settings (10 second checks)...
goto start_custom

:start_default
echo Starting Game Keeper with default settings...
powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1"
goto end

:start_custom
echo Starting Game Keeper with custom settings...
if "%VERBOSE%"=="true" (
    powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1" -CheckInterval %CHECK_INTERVAL% -RestartDelay %RESTART_DELAY% -MaxRestartAttempts %MAX_ATTEMPTS% -CooldownMinutes %COOLDOWN_MINUTES% -Verbose
) else (
    powershell.exe -ExecutionPolicy Bypass -File "game-keeper.ps1" -CheckInterval %CHECK_INTERVAL% -RestartDelay %RESTART_DELAY% -MaxRestartAttempts %MAX_ATTEMPTS% -CooldownMinutes %COOLDOWN_MINUTES%
)
goto end

:invalid_choice
echo Invalid choice. Please enter 1-5.
pause
goto start

:exit
echo Exiting...
goto end

:end
echo.
echo Game Keeper has stopped.
pause
